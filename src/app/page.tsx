import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-indigo-600">AfireAI</h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="#features" className="text-gray-700 hover:text-indigo-600">機能</a>
              <a href="#workflow" className="text-gray-700 hover:text-indigo-600">ワークフロー</a>
              <a href="#chatbot" className="text-gray-700 hover:text-indigo-600">チャットボット</a>
              <a href="#agent" className="text-gray-700 hover:text-indigo-600">AIエージェント</a>
            </nav>
            <div className="flex space-x-4">
              <button className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                無料で始める
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              AIアプリケーション開発を
              <span className="text-indigo-600">数分で実現</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              エージェントワークフロー、RAGシステム、モニタリングを一つに統合した、
              AIアプリケーション開発プラットフォーム。ドラッグ＆ドロップによるノーコードUIで、
              アイデアから本番運用まで数分で実現します。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-indigo-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-indigo-700 transition-colors">
                今すぐ始める
              </button>
              <button className="border border-indigo-600 text-indigo-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-indigo-50 transition-colors">
                デモを見る
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              AfireAIの核心機能
            </h2>
            <p className="text-xl text-gray-600">
              プログラミングスキルがなくても、直感的な操作でAIアプリを作成
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">ノーコード開発</h3>
              <p className="text-gray-600">ドラッグ＆ドロップの簡単操作で誰でもAIアプリを構築</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">迅速な市場投入</h3>
              <p className="text-gray-600">複雑な設定は不要。記録的な速さでアイデアを形に</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">マルチLLM対応</h3>
              <p className="text-gray-600">GPT-4o、Claude 3、Gemini、Llama-3を自由に切り替え</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">RAG標準搭載</h3>
              <p className="text-gray-600">外部データを即座にAIの知識として活用</p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Features */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              3つの主要機能
            </h2>
            <p className="text-xl text-gray-600">
              テンプレートから簡単に作成・運用できるAIアプリケーション
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Workflow */}
            <div id="workflow" className="bg-white rounded-xl shadow-lg p-8">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">ワークフロー作成</h3>
              <p className="text-gray-600 mb-6">
                特定のトリガーに応じて一連の処理を自動的に実行。直感的なUIからステップを構築でき、非エンジニアでも安心して扱えます。
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• 問い合わせ内容の自動分類・返信</li>
                <li>• 入力内容の要約・レポート作成</li>
                <li>• 商品レビューの解析・記録</li>
                <li>• 資料作成・報告業務の自動化</li>
              </ul>
            </div>

            {/* Chatbot */}
            <div id="chatbot" className="bg-white rounded-xl shadow-lg p-8">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">チャットボット作成</h3>
              <p className="text-gray-600 mb-6">
                ChatGPTなどのLLMを活用したAIチャットボットをノーコードで作成。社内FAQやマニュアルのAI化に最適です。
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• RAG機能による外部データ連携</li>
                <li>• Prompt-as-App構造でUI化</li>
                <li>• 複数LLMモデルの切り替え対応</li>
                <li>• ユーザーフレンドリーなインターフェース</li>
              </ul>
            </div>

            {/* AI Agent */}
            <div id="agent" className="bg-white rounded-xl shadow-lg p-8">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">AIエージェント作成</h3>
              <p className="text-gray-600 mb-6">
                複雑なタスクを自動で分解し、段階的に実行。AIが「対話相手」から「業務を補佐する実行者」へと進化します。
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• 競合分析の自動収集・要約</li>
                <li>• タスクのToDoリスト変換</li>
                <li>• マーケティング戦略案の提案</li>
                <li>• 業務の属人化防止</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Workflow Nodes Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              よく使うノード一覧（ワークフロー構築用）
            </h2>
            <p className="text-xl text-gray-600">
              AfireAIのワークフロー構築において、直感的に利用できる主要なノード群です。
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Start Node */}
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900">開始 (Start)</h3>
              </div>
              <p className="text-gray-600 mb-3 text-sm">
                ワークフローのエントリポイント。トリガー種別（手動 / API / スケジュール）を設定可能。
              </p>
              <div className="bg-white rounded-lg p-3 border border-green-200">
                <p className="text-xs text-gray-500 mb-1">典型的ユースケース:</p>
                <p className="text-sm text-gray-700">毎日 9:00 にレポート生成を開始。</p>
              </div>
            </div>

            {/* Approval Node */}
            <div className="bg-gradient-to-br from-yellow-50 to-amber-50 rounded-xl p-6 border border-yellow-100">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900">承認待ち (Approval)</h3>
              </div>
              <p className="text-gray-600 mb-3 text-sm">
                指定ユーザーの承認を待機する。
              </p>
              <div className="bg-white rounded-lg p-3 border border-yellow-200">
                <p className="text-xs text-gray-500 mb-1">典型的ユースケース:</p>
                <p className="text-sm text-gray-700">経費精算フローの部長承認。</p>
              </div>
            </div>

            {/* HTTP Request Node */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900">HTTP リクエスト</h3>
              </div>
              <p className="text-gray-600 mb-3 text-sm">
                REST/GraphQLなどの外部APIを呼び出す。
              </p>
              <div className="bg-white rounded-lg p-3 border border-blue-200">
                <p className="text-xs text-gray-500 mb-1">典型的ユースケース:</p>
                <p className="text-sm text-gray-700">Slack Webhook通知、CRM API更新。</p>
              </div>
            </div>

            {/* LLM Node */}
            <div className="bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6 border border-purple-100">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900">LLM (AI)</h3>
              </div>
              <p className="text-gray-600 mb-3 text-sm">
                指定のLLMを呼び出し、文章生成などを行う。
              </p>
              <div className="bg-white rounded-lg p-3 border border-purple-200">
                <p className="text-xs text-gray-500 mb-1">典型的ユースケース:</p>
                <p className="text-sm text-gray-700">FAQ回答生成、文章要約。</p>
              </div>
            </div>

            {/* Knowledge Search Node */}
            <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 border border-orange-100">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900">ナレッジ検索</h3>
              </div>
              <p className="text-gray-600 mb-3 text-sm">
                ベクトルDBに対して検索を行う。
              </p>
              <div className="bg-white rounded-lg p-3 border border-orange-200">
                <p className="text-xs text-gray-500 mb-1">典型的ユースケース:</p>
                <p className="text-sm text-gray-700">社内マニュアル参照。</p>
              </div>
            </div>

            {/* Python Code Node */}
            <div className="bg-gradient-to-br from-teal-50 to-cyan-50 rounded-xl p-6 border border-teal-100">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900">Python コード</h3>
              </div>
              <p className="text-gray-600 mb-3 text-sm">
                任意のPythonスクリプトを実行する。
              </p>
              <div className="bg-white rounded-lg p-3 border border-teal-200">
                <p className="text-xs text-gray-500 mb-1">典型的ユースケース:</p>
                <p className="text-sm text-gray-700">データ整形、PDF生成。</p>
              </div>
            </div>

            {/* End Node */}
            <div className="bg-gradient-to-br from-red-50 to-pink-50 rounded-xl p-6 border border-red-100 md:col-span-2 lg:col-span-1">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900">終了 (End)</h3>
              </div>
              <p className="text-gray-600 mb-3 text-sm">
                ワークフローの終了点。終了ステータスや出力の設定が可能。
              </p>
              <div className="bg-white rounded-lg p-3 border border-red-200">
                <p className="text-xs text-gray-500 mb-1">典型的ユースケース:</p>
                <p className="text-sm text-gray-700">成功／失敗のステータス返却。</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* RAG Feature */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                RAG機能で知識を拡張
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                外部データを取り込んでAIに新たな知識を与え、回答の精度を向上させる技術。
                社内マニュアルやFAQ、過去の会議議事録などを「AIの知識」として組み込み、
                社内ナレッジの活用効率を飛躍的に向上させます。
              </p>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-gray-700">CSV、PDF、TXT、JSONなど幅広いファイル形式に対応</span>
                </div>
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-gray-700">アップロードした情報がリアルタイムで検索対象に</span>
                </div>
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-gray-700">全文一致検索だけでなく、意味検索にも対応</span>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-8">
              <div className="text-center">
                <div className="w-24 h-24 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-12 h-12 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">ベクトルデータベース</h3>
                <p className="text-gray-600">
                  多様なデータソースからデータを抽出し、変換、インデックス化。
                  LLMでの活用に最適な形でベクトルデータベースに格納します。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Infrastructure */}
      <section className="py-20 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              堅牢なAIインフラストラクチャ
            </h2>
            <p className="text-xl text-gray-300">
              企業のAIトランスフォーメーションを支える確固たるインフラ基盤
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">スケーラビリティ</h3>
              <p className="text-gray-300">
                KubernetesベースのAuto-Scalingにより、アクセス急増時も安定したサービス運用が可能
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">セキュリティ</h3>
              <p className="text-gray-300">
                エンタープライズ認証（SAML/OIDC）など、エンタープライズレベルのセキュリティで重要なデータ資産を保護
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">安定性</h3>
              <p className="text-gray-300">
                堅牢な基盤により、安心してサービスを運用いただけます。増加するトラフィックにも柔軟に対応
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-indigo-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            今すぐAfireAIを始めましょう
          </h2>
          <p className="text-xl text-indigo-100 mb-8">
            AIのアイデア検証から事業成長の追求まで、真にインパクトのある製品を創り出すために必要なすべてを提供します
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-indigo-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors">
              無料で始める
            </button>
            <button className="border border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-indigo-700 transition-colors">
              お問い合わせ
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">AfireAI</h3>
              <p className="text-gray-400">
                AIアプリケーション開発プラットフォーム。ノーコードでAIの力をあなたの手元に。
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">製品</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#workflow" className="hover:text-white">ワークフロー</a></li>
                <li><a href="#chatbot" className="hover:text-white">チャットボット</a></li>
                <li><a href="#agent" className="hover:text-white">AIエージェント</a></li>
                <li><a href="#" className="hover:text-white">RAG機能</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">リソース</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">ドキュメント</a></li>
                <li><a href="#" className="hover:text-white">チュートリアル</a></li>
                <li><a href="#" className="hover:text-white">API リファレンス</a></li>
                <li><a href="#" className="hover:text-white">サポート</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">会社</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">会社概要</a></li>
                <li><a href="#" className="hover:text-white">お問い合わせ</a></li>
                <li><a href="#" className="hover:text-white">プライバシーポリシー</a></li>
                <li><a href="#" className="hover:text-white">利用規約</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 AfireAI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
